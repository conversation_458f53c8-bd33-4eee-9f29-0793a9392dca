# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Orchestrator Agent - Intelligent Question Routing
Routes user questions to appropriate specialized agents using ADK AgentTool
"""

import logging
import sys
import os
import importlib.util
from pathlib import Path
from google.adk.agents import Agent
from google.adk.tools.agent_tool import AgentTool

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Set environment variables
os.environ.setdefault('DATABASE_URL', 'postgresql+asyncpg://postgres:<EMAIL>:5432/postgres')

def load_specialized_agents():
    """Load the specialized Q&A and Orders agents as AgentTools"""
    qa_agent_tool = None
    orders_agent_tool = None

    # Load Orders Agent
    try:
        orders_path = Path(__file__).parent.parent / "orders_agent_web"
        if str(orders_path) not in sys.path:
            sys.path.insert(0, str(orders_path))

        # Import with a unique name to avoid conflicts
        spec = importlib.util.spec_from_file_location("orders_agent_module", orders_path / "agent.py")
        orders_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(orders_module)

        # Wrap the agent as a tool
        orders_agent_tool = AgentTool(orders_module.root_agent)
        logger.info("✅ Orders Agent loaded successfully as AgentTool")

    except Exception as e:
        logger.error(f"❌ Failed to load Orders Agent: {e}")
        raise RuntimeError(f"Orders Agent is required but failed to load: {e}")

    # Load Q&A Agent
    try:
        qa_path = Path(__file__).parent.parent / "qa_agent" / "src"

        # Clear sys.modules of any conflicting modules
        modules_to_clear = [k for k in sys.modules.keys() if k in ['database', 'embeddings', 'document_processor']]
        for module in modules_to_clear:
            del sys.modules[module]

        # Clear sys.path and add only Q&A agent path
        original_path = sys.path.copy()
        sys.path.clear()
        sys.path.extend([str(qa_path), '/home/<USER>/miniconda3/lib/python312.zip', '/home/<USER>/miniconda3/lib/python3.12', '/home/<USER>/miniconda3/lib/python3.12/lib-dynload', '/home/<USER>/miniconda3/lib/python3.12/site-packages'])

        # Import with a unique name to avoid conflicts
        spec = importlib.util.spec_from_file_location("qa_agent_module", qa_path / "agent.py")
        qa_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(qa_module)

        # Restore original path
        sys.path.clear()
        sys.path.extend(original_path)

        # Wrap the agent as a tool
        qa_agent_tool = AgentTool(qa_module.root_agent)
        logger.info("✅ Q&A Agent loaded successfully as AgentTool")

    except Exception as e:
        logger.error(f"❌ Failed to load Q&A Agent: {e}")
        raise RuntimeError(f"Q&A Agent is required but failed to load: {e}")

    return qa_agent_tool, orders_agent_tool

# Load the specialized agents
qa_agent_tool, orders_agent_tool = load_specialized_agents()

# Create the orchestrator agent using ADK Agent with AgentTools
root_agent = Agent(
    name="orchestrator_agent",
    description="Intelligent orchestrator that routes questions to specialized agents",
    model="gemini-2.0-flash",
    tools=[qa_agent_tool, orders_agent_tool],
    instruction="""
    You are an intelligent orchestrator that routes user questions to specialized agents.

    AVAILABLE TOOLS:
    1. **dropi_qa_agent**: Use for questions about Dropi company, services, academy, tutorials, and general company information
    2. **orders_management_agent**: Use for questions about orders, sales, order management, statistics, order status, and commercial operations

    ROUTING LOGIC:
    - Analyze the user's question to understand what they're asking about
    - If it's about Dropi (company info, services, academy): use dropi_qa_agent
    - If it's about orders, sales, statistics: use orders_management_agent
    - Always call the appropriate tool with the user's question as the request parameter

    EXAMPLES:
    - "¿Qué es Dropi?" → use dropi_qa_agent
    - "¿Cuántas órdenes hay?" → use orders_management_agent
    - "¿Cómo funciona Dropi Academy?" → use dropi_qa_agent
    - "¿Qué órdenes están pendientes?" → use orders_management_agent

    Always provide the complete response from the specialized agent.
    """
)

logger.info("🎭 Orchestrator Agent initialized successfully")
