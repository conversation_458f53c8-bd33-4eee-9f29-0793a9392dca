# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Orchestrator Agent Web Interface
FastAPI application for the intelligent question routing orchestrator
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
import logging
import sys
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import the orchestrator agent
try:
    # Add the current directory to the path to import the agent
    current_dir = Path(__file__).parent
    sys.path.insert(0, str(current_dir))

    from agent import root_agent
    logger.info("✅ Orchestrator agent imported successfully")
except Exception as e:
    logger.error(f"❌ Failed to import orchestrator agent: {e}")
    root_agent = None

app = FastAPI(title="Intelligent Orchestrator Agent", description="Automatically routes questions to specialized agents")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class ChatRequest(BaseModel):
    message: str

class ChatResponse(BaseModel):
    response: str
    error: str = None

@app.get("/", response_class=HTMLResponse)
async def get_chat_interface():
    """Serve the orchestrator chat interface"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Intelligent Orchestrator Agent</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
            .container { max-width: 900px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
            .header { text-align: center; color: #333; margin-bottom: 30px; }
            .header h1 { color: #667eea; margin-bottom: 10px; }
            .subtitle { color: #666; font-size: 18px; margin-bottom: 20px; }
            .agent-info { background: #f8f9ff; padding: 15px; border-radius: 10px; margin-bottom: 20px; border-left: 4px solid #667eea; }
            .chat-container { border: 2px solid #e0e6ff; height: 450px; overflow-y: auto; padding: 15px; margin-bottom: 20px; background: #fafbff; border-radius: 10px; }
            .message { margin: 15px 0; padding: 12px; border-radius: 10px; }
            .user-message { background: linear-gradient(135deg, #667eea, #764ba2); color: white; text-align: right; }
            .agent-message { background: #e8f2ff; border-left: 4px solid #667eea; }
            .qa-agent { border-left-color: #4caf50; background: #e8f5e8; }
            .orders-agent { border-left-color: #ff9800; background: #fff3e0; }
            .input-container { display: flex; gap: 15px; }
            .message-input { flex: 1; padding: 12px; border: 2px solid #e0e6ff; border-radius: 8px; font-size: 16px; }
            .message-input:focus { outline: none; border-color: #667eea; }
            .send-button { padding: 12px 25px; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 16px; font-weight: bold; }
            .send-button:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4); }
            .examples { margin-top: 25px; }
            .example-section { margin: 15px 0; }
            .example-section h4 { color: #333; margin-bottom: 10px; }
            .example-button { margin: 5px; padding: 10px 15px; border: 2px solid #e0e6ff; border-radius: 8px; cursor: pointer; background: white; transition: all 0.3s; }
            .example-button:hover { background: #667eea; color: white; transform: translateY(-1px); }
            .qa-example { border-color: #4caf50; }
            .qa-example:hover { background: #4caf50; }
            .orders-example { border-color: #ff9800; }
            .orders-example:hover { background: #ff9800; }
            .routing-indicator { font-size: 12px; color: #666; font-style: italic; margin-bottom: 5px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🎭 Intelligent Orchestrator Agent</h1>
                <p class="subtitle">Automatically routes your questions to the right specialist</p>
            </div>
            
            <div class="agent-info">
                <strong>🤖 How it works:</strong> I analyze your question and automatically route it to the appropriate specialist:
                <br>• <strong style="color: #4caf50;">📚 Q&A Agent</strong> - Dropi company information, services, academy
                <br>• <strong style="color: #ff9800;">🛒 Orders Agent</strong> - Order management, sales statistics, order status
            </div>
            
            <div class="chat-container" id="chatContainer">
                <div class="message agent-message">
                    <div class="routing-indicator">🎭 Orchestrator Agent</div>
                    <strong>Orchestrator:</strong> ¡Hola! Soy el agente orquestador inteligente. Analizo automáticamente tus preguntas y las dirijo al especialista correcto. Puedes preguntarme sobre Dropi o sobre gestión de órdenes, y yo me encargaré de dirigir tu consulta al agente adecuado. ¿En qué puedo ayudarte?
                </div>
            </div>
            
            <div class="input-container">
                <input type="text" id="messageInput" class="message-input" placeholder="Haz cualquier pregunta sobre Dropi u órdenes..." onkeypress="handleKeyPress(event)">
                <button class="send-button" onclick="sendMessage()">Enviar</button>
            </div>
            
            <div class="examples">
                <div class="example-section">
                    <h4>📚 Preguntas sobre Dropi (Q&A Agent):</h4>
                    <button class="example-button qa-example" onclick="setMessage('¿Qué es Dropi?')">¿Qué es Dropi?</button>
                    <button class="example-button qa-example" onclick="setMessage('¿Cómo funciona Dropi Academy?')">¿Cómo funciona Dropi Academy?</button>
                    <button class="example-button qa-example" onclick="setMessage('¿Cuáles son los servicios de Dropi?')">¿Cuáles son los servicios de Dropi?</button>
                </div>
                
                <div class="example-section">
                    <h4>🛒 Preguntas sobre Órdenes (Orders Agent):</h4>
                    <button class="example-button orders-example" onclick="setMessage('¿Cuántas órdenes hay?')">¿Cuántas órdenes hay?</button>
                    <button class="example-button orders-example" onclick="setMessage('¿Qué órdenes están pendientes?')">¿Qué órdenes están pendientes?</button>
                    <button class="example-button orders-example" onclick="setMessage('¿Cuáles son las estadísticas de órdenes?')">¿Cuáles son las estadísticas de órdenes?</button>
                </div>
            </div>
        </div>

        <script>
            function handleKeyPress(event) {
                if (event.key === 'Enter') {
                    sendMessage();
                }
            }

            function setMessage(message) {
                document.getElementById('messageInput').value = message;
            }

            async function sendMessage() {
                const input = document.getElementById('messageInput');
                const message = input.value.trim();
                if (!message) return;

                const chatContainer = document.getElementById('chatContainer');
                
                // Add user message
                const userDiv = document.createElement('div');
                userDiv.className = 'message user-message';
                userDiv.innerHTML = `<strong>Usuario:</strong> ${message}`;
                chatContainer.appendChild(userDiv);
                
                // Clear input
                input.value = '';
                
                // Scroll to bottom
                chatContainer.scrollTop = chatContainer.scrollHeight;
                
                try {
                    const response = await fetch('/chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ message: message })
                    });
                    
                    const data = await response.json();
                    
                    // Add agent response with routing indicator
                    const agentDiv = document.createElement('div');
                    
                    // Determine agent type and styling
                    let agentClass = 'agent-message';
                    let routingInfo = '🎭 Orchestrator';

                    if (data.response && data.response.includes && data.response.includes('[Q&A Agent]')) {
                        agentClass += ' qa-agent';
                        routingInfo = '📚 Routed to Q&A Agent';
                    } else if (data.response && data.response.includes && data.response.includes('[Orders Agent]')) {
                        agentClass += ' orders-agent';
                        routingInfo = '🛒 Routed to Orders Agent';
                    }
                    
                    agentDiv.className = `message ${agentClass}`;
                    
                    if (data.error) {
                        agentDiv.innerHTML = `<div class="routing-indicator">${routingInfo}</div><strong>Error:</strong> ${data.error}`;
                    } else {
                        // Clean up the response to remove agent prefixes
                        let cleanResponse = data.response || '';
                        if (cleanResponse.includes && cleanResponse.includes('[')) {
                            cleanResponse = cleanResponse.replace(/^\[.*?\]\s*/, '');
                        }
                        agentDiv.innerHTML = `<div class="routing-indicator">${routingInfo}</div><strong>Respuesta:</strong> ${cleanResponse}`;
                    }
                    chatContainer.appendChild(agentDiv);
                    
                } catch (error) {
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'message agent-message';
                    errorDiv.innerHTML = `<div class="routing-indicator">❌ Error</div><strong>Error de conexión:</strong> ${error.message}`;
                    chatContainer.appendChild(errorDiv);
                }
                
                // Scroll to bottom
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

@app.post("/chat", response_model=ChatResponse)
async def chat_with_orchestrator(request: ChatRequest):
    """Handle chat requests with the orchestrator agent"""
    if not root_agent:
        raise HTTPException(status_code=500, detail="Orchestrator agent not available")
    
    try:
        logger.info(f"🎭 Orchestrator processing query: {request.message}")
        
        # Use the orchestrator's routing functionality
        response_parts = []
        async for chunk in root_agent.run_async(request.message):
            response_parts.append(str(chunk))
        response = ''.join(response_parts)
        
        logger.info(f"✅ Orchestrator response ready: {response[:100]}...")
        return ChatResponse(response=response)
        
    except Exception as e:
        logger.error(f"❌ Error processing orchestrator request: {e}")
        return ChatResponse(response="", error=f"Error: {str(e)}")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "agent_available": root_agent is not None,
        "agent_name": "orchestrator_agent" if root_agent else None
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8003)
